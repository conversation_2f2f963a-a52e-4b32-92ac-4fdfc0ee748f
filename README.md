# Swickard Enterprise Data Warehouse

Azure Data Factory implementation for automotive dealership data integration. Processes Reynolds DMS XML feeds, FTP vendor data, and CSV files through a three-layer architecture (Bronze/Silver/Gold) with 27+ business entities including Customer, Vehicle, and Deal management.

## Technical Overview

- **Data Sources**: Reynolds DMS XML, FTP servers, CSV files, third-party APIs
- **Architecture**: Data Vault 2.0 with Bronze/Silver/Gold layers
- **Processing**: Real-time sales leads + batch vendor data
- **Storage**: Azure Data Lake Gen2 + Azure SQL Database
- **Compute**: Azure Functions for ETL processing
- **Monitoring**: Azure Monitor with custom dashboards

## 🏗️ Project Structure

```
swickard-adf/
├── .vscode/                    # VS Code configuration
├── src/
│   ├── datafactory/           # ADF artifacts (Git-connected)
│   │   ├── pipeline/          # Pipeline definitions
│   │   ├── dataset/           # Dataset definitions
│   │   ├── linkedService/     # Linked service definitions
│   │   ├── dataflow/          # Data flow definitions
│   │   ├── trigger/           # Trigger definitions
│   │   └── integrationRuntime/ # Integration runtime definitions
│   ├── functions/             # Azure Functions code
│   ├── notebooks/             # Databricks/Synapse notebooks
│   └── sql/                   # SQL scripts and stored procedures
├── config/                    # Environment configurations
├── deployment/                # Deployment scripts and templates
├── tests/                     # Test suites
└── docs/                      # Project documentation
```

## 🚀 Quick Start

### Prerequisites
- VS Code with Azure extensions
- Azure CLI
- PowerShell 7+
- Git
- Azure subscription access

### Setup
1. **Clone and setup:**
   ```bash
   cd swickard-adf
   git init
   git remote add origin <your-repo-url>
   ```

2. **Install dependencies:**
   ```bash
   # Install Azure CLI extensions
   az extension add --name datafactory
   
   # Install PowerShell modules
   Install-Module -Name Az -Force
   Install-Module -Name azure.datafactory.tools -Force
   ```

3. **Configure Azure:**
   ```bash
   az login
   az account set --subscription "your-subscription-id"
   ```

4. **Setup development environment:**
   ```bash
   # Run setup script
   ./deployment/setup-dev-environment.ps1
   ```

## 🔧 Development Workflow

### 1. Feature Development
```bash
# Create feature branch
git checkout -b feature/customer-data-pipeline

# Develop in VS Code with ADF Studio integration
# Test locally using debug mode
# Validate artifacts
./deployment/validate.ps1

# Commit and push
git add .
git commit -m "feat: add customer data ingestion pipeline"
git push origin feature/customer-data-pipeline
```

### 2. Testing
```bash
# Run all tests
./tests/run-tests.ps1

# Run specific test category
./tests/run-tests.ps1 -Category Unit
./tests/run-tests.ps1 -Category Integration
```

### 3. Deployment
```bash
# Deploy to development
./deployment/deploy.ps1 -Environment dev

# Deploy to test
./deployment/deploy.ps1 -Environment test

# Deploy to production (requires approval)
./deployment/deploy.ps1 -Environment prod
```

## Data Processing

### Current Production Pipelines
- `pl_ingest_SalesLead`: Real-time Reynolds XML processing (37 field mappings)
- `PL_Bronze`: Batch processing via Function Apps (FTP + CSV)
- `PL_Silver`: Business entity transformation to Data Vault model

### Function Apps
- `func-swickard-etl-v2`: FTP data retrieval and processing
- `csv-processing-func-3514`: CSV file validation and transformation

### Data Model
- **Silver Layer**: 27+ tables including Customer, Vehicle, Deal entities
- **Pattern**: Data Vault 2.0 (Hub, Satellite, Link tables)
- **Relationships**: Customer ↔ Deal ↔ Vehicle with full audit trail

## Documentation

### Business Analysis
- [Local Development Business Case](docs/LOCAL_DEVELOPMENT_BUSINESS_CASE.md) - ROI analysis and risk assessment
- [AI Development Capabilities](docs/AI_DEVELOPMENT_CAPABILITIES.md) - AI-assisted development features
- [Data Sources & Function Apps](docs/DATA_SOURCES_AND_FUNCTION_APPS.md) - Integration architecture

### Architecture Diagrams
- [Overall Architecture](docs/diagrams/01_OVERALL_ARCHITECTURE.md) - Complete system overview
- [Pipeline Orchestration](docs/diagrams/02_PIPELINE_ORCHESTRATION.md) - Data processing workflows
- [Data Model ERD](docs/diagrams/03_DATA_MODEL_ERD.md) - Entity relationships and schema
- [Data Flow & Lineage](docs/diagrams/04_DATA_FLOW_LINEAGE.md) - End-to-end data lineage
- [Security Architecture](docs/diagrams/05_SECURITY_ARCHITECTURE.md) - Security controls and compliance
- [Deployment Architecture](docs/diagrams/06_DEPLOYMENT_ARCHITECTURE.md) - CI/CD and environment strategy

### Technical Details
- [Silver Vehicle Data Model](docs/SILVER_VEHICLE_DATA_MODEL.md) - Vehicle entity analysis

## Security & Compliance

- **Authentication**: Azure AD + Managed Identity
- **Encryption**: TDE for SQL, AES-256 for Data Lake
- **Network**: Private endpoints, Managed VNet
- **Compliance**: GDPR consent tracking, audit trails
- **Issues Identified**: Function Apps using anonymous authentication (requires remediation)

## Performance Metrics

- **Sales Lead Processing**: < 5 minutes per XML file
- **Bronze Pipeline**: 30-45 minutes for full batch
- **Data Volume**: 100K+ records per day
- **Availability**: Production SLA targets

## Environment Status

- **Development**: Local development with AI assistance
- **Testing**: Automated validation and integration tests
- **Production**: SwickardEDW Data Factory (West US)
