# Swickard ADF Unit Tests
# Unit tests for ADF artifacts and configurations

BeforeAll {
    $ProjectRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
    $ConfigPath = Join-Path $ProjectRoot "config"
    $ArtifactPath = Join-Path $ProjectRoot "src/datafactory"
}

Describe "Swickard ADF Configuration Tests" {
    Context "Environment Configuration Files" {
        It "Should have valid dev configuration" {
            $devConfig = Join-Path $ConfigPath "dev.json"
            $devConfig | Should -Exist
            
            $config = Get-Content $devConfig | ConvertFrom-Json
            $config.subscriptionId | Should -Not -BeNullOrEmpty
            $config.projectName | Should -Be "swickard"
            $config.environment | Should -Be "dev"
            $config.dataFactory.name | Should -Be "adf-swickard-dev"
        }
        
        It "Should have valid test configuration" {
            $testConfig = Join-Path $ConfigPath "test.json"
            $testConfig | Should -Exist
            
            $config = Get-Content $testConfig | ConvertFrom-Json
            $config.subscriptionId | Should -Not -BeNullOrEmpty
            $config.projectName | Should -Be "swickard"
            $config.environment | Should -Be "test"
            $config.dataFactory.name | Should -Be "adf-swickard-test"
        }
        
        It "Should have valid prod configuration" {
            $prodConfig = Join-Path $ConfigPath "prod.json"
            $prodConfig | Should -Exist
            
            $config = Get-Content $prodConfig | ConvertFrom-Json
            $config.subscriptionId | Should -Not -BeNullOrEmpty
            $config.projectName | Should -Be "swickard"
            $config.environment | Should -Be "prod"
            $config.dataFactory.name | Should -Be "adf-swickard-prod"
        }
        
        It "Should have consistent structure across environments" {
            $environments = @("dev", "test", "prod")
            $requiredProperties = @(
                "subscriptionId", "projectName", "environment", 
                "dataFactory", "linkedServices", "integrationRuntimes",
                "monitoring", "security", "tags"
            )
            
            foreach ($env in $environments) {
                $configFile = Join-Path $ConfigPath "$env.json"
                $config = Get-Content $configFile | ConvertFrom-Json
                
                foreach ($property in $requiredProperties) {
                    $config.$property | Should -Not -BeNull -Because "Property '$property' should exist in $env configuration"
                }
            }
        }
    }
    
    Context "Naming Conventions" {
        It "Should follow Azure naming conventions" {
            $environments = @("dev", "test", "prod")
            
            foreach ($env in $environments) {
                $configFile = Join-Path $ConfigPath "$env.json"
                $config = Get-Content $configFile | ConvertFrom-Json
                
                # Data Factory naming
                $config.dataFactory.name | Should -Match "^adf-swickard-$env$"
                
                # Resource Group naming
                $config.dataFactory.resourceGroup | Should -Match "^rg-swickard-adf-$env$"
                
                # Storage Account naming (no hyphens, lowercase)
                $config.linkedServices.storageAccount.accountName | Should -Match "^stswickard$env$"
                
                # Key Vault naming
                $config.linkedServices.keyVault.name | Should -Match "^kv-swickard-$env$"
            }
        }
    }
}

Describe "Swickard ADF Pipeline Tests" {
    Context "Pipeline Structure Validation" {
        BeforeAll {
            $PipelinePath = Join-Path $ArtifactPath "pipeline"
        }
        
        It "Should have pipeline directory" {
            $PipelinePath | Should -Exist
        }
        
        It "Should validate pipeline JSON files if they exist" {
            if (Test-Path $PipelinePath) {
                $pipelines = Get-ChildItem -Path $PipelinePath -Filter "*.json" -Recurse
                
                foreach ($pipeline in $pipelines) {
                    { Get-Content $pipeline.FullName | ConvertFrom-Json } | Should -Not -Throw -Because "Pipeline $($pipeline.Name) should have valid JSON"
                }
            }
        }
        
        It "Should have required pipeline properties if pipelines exist" {
            if (Test-Path $PipelinePath) {
                $pipelines = Get-ChildItem -Path $PipelinePath -Filter "*.json" -Recurse
                
                foreach ($pipeline in $pipelines) {
                    $content = Get-Content $pipeline.FullName | ConvertFrom-Json
                    $content.name | Should -Not -BeNullOrEmpty -Because "Pipeline should have a name"
                    $content.properties | Should -Not -BeNull -Because "Pipeline should have properties"
                    $content.properties.activities | Should -Not -BeNull -Because "Pipeline should have activities"
                }
            }
        }
    }
}

Describe "Swickard ADF Dataset Tests" {
    Context "Dataset Structure Validation" {
        BeforeAll {
            $DatasetPath = Join-Path $ArtifactPath "dataset"
        }
        
        It "Should have dataset directory" {
            $DatasetPath | Should -Exist
        }
        
        It "Should validate dataset JSON files if they exist" {
            if (Test-Path $DatasetPath) {
                $datasets = Get-ChildItem -Path $DatasetPath -Filter "*.json" -Recurse
                
                foreach ($dataset in $datasets) {
                    { Get-Content $dataset.FullName | ConvertFrom-Json } | Should -Not -Throw -Because "Dataset $($dataset.Name) should have valid JSON"
                }
            }
        }
        
        It "Should have required dataset properties if datasets exist" {
            if (Test-Path $DatasetPath) {
                $datasets = Get-ChildItem -Path $DatasetPath -Filter "*.json" -Recurse
                
                foreach ($dataset in $datasets) {
                    $content = Get-Content $dataset.FullName | ConvertFrom-Json
                    $content.name | Should -Not -BeNullOrEmpty -Because "Dataset should have a name"
                    $content.properties | Should -Not -BeNull -Because "Dataset should have properties"
                    $content.properties.type | Should -Not -BeNullOrEmpty -Because "Dataset should have a type"
                }
            }
        }
    }
}

Describe "Swickard ADF Linked Service Tests" {
    Context "Linked Service Structure Validation" {
        BeforeAll {
            $LinkedServicePath = Join-Path $ArtifactPath "linkedService"
        }
        
        It "Should have linkedService directory" {
            $LinkedServicePath | Should -Exist
        }
        
        It "Should validate linked service JSON files if they exist" {
            if (Test-Path $LinkedServicePath) {
                $linkedServices = Get-ChildItem -Path $LinkedServicePath -Filter "*.json" -Recurse
                
                foreach ($linkedService in $linkedServices) {
                    { Get-Content $linkedService.FullName | ConvertFrom-Json } | Should -Not -Throw -Because "Linked Service $($linkedService.Name) should have valid JSON"
                }
            }
        }
        
        It "Should have required linked service properties if they exist" {
            if (Test-Path $LinkedServicePath) {
                $linkedServices = Get-ChildItem -Path $LinkedServicePath -Filter "*.json" -Recurse
                
                foreach ($linkedService in $linkedServices) {
                    $content = Get-Content $linkedService.FullName | ConvertFrom-Json
                    $content.name | Should -Not -BeNullOrEmpty -Because "Linked Service should have a name"
                    $content.properties | Should -Not -BeNull -Because "Linked Service should have properties"
                    $content.properties.type | Should -Not -BeNullOrEmpty -Because "Linked Service should have a type"
                }
            }
        }
    }
}

Describe "Swickard ADF Security Tests" {
    Context "Security Configuration" {
        It "Should use Key Vault for secrets in all environments" {
            $environments = @("dev", "test", "prod")
            
            foreach ($env in $environments) {
                $configFile = Join-Path $ConfigPath "$env.json"
                $config = Get-Content $configFile | ConvertFrom-Json
                
                $config.linkedServices.keyVault | Should -Not -BeNull -Because "$env should have Key Vault configuration"
                $config.linkedServices.keyVault.name | Should -Not -BeNullOrEmpty -Because "$env should have Key Vault name"
                $config.linkedServices.keyVault.baseUrl | Should -Match "^https://.*\.vault\.azure\.net/$" -Because "$env should have valid Key Vault URL"
            }
        }
        
        It "Should have managed identity enabled" {
            $environments = @("dev", "test", "prod")
            
            foreach ($env in $environments) {
                $configFile = Join-Path $ConfigPath "$env.json"
                $config = Get-Content $configFile | ConvertFrom-Json
                
                $config.security.managedIdentity.enabled | Should -Be $true -Because "$env should have managed identity enabled"
                $config.security.managedIdentity.type | Should -Be "SystemAssigned" -Because "$env should use system-assigned managed identity"
            }
        }
        
        It "Should have RBAC configuration" {
            $environments = @("dev", "test", "prod")
            
            foreach ($env in $environments) {
                $configFile = Join-Path $ConfigPath "$env.json"
                $config = Get-Content $configFile | ConvertFrom-Json
                
                $config.security.rbac | Should -Not -BeNull -Because "$env should have RBAC configuration"
                $config.security.rbac.dataFactoryContributor | Should -Not -BeNull -Because "$env should have contributor roles defined"
                $config.security.rbac.dataFactoryReader | Should -Not -BeNull -Because "$env should have reader roles defined"
            }
        }
    }
}

Describe "Swickard ADF Deployment Scripts" {
    Context "PowerShell Script Validation" {
        It "Should have deployment script" {
            $deployScript = Join-Path $ProjectRoot "deployment/deploy.ps1"
            $deployScript | Should -Exist
        }
        
        It "Should have validation script" {
            $validateScript = Join-Path $ProjectRoot "deployment/validate.ps1"
            $validateScript | Should -Exist
        }
        
        It "Should have setup script" {
            $setupScript = Join-Path $ProjectRoot "deployment/setup-dev-environment.ps1"
            $setupScript | Should -Exist
        }
        
        It "Should have test runner script" {
            $testScript = Join-Path $ProjectRoot "tests/run-tests.ps1"
            $testScript | Should -Exist
        }
    }
}
