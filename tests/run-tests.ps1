# Swickard ADF Test Runner
# Runs various categories of tests for the Swickard ADF project

param(
    [ValidateSet("All", "Unit", "Integration", "Validation", "Performance")]
    [string]$Category = "All",
    
    [string]$TestPath = "tests",
    [switch]$GenerateReport,
    [switch]$Verbose
)

$ErrorActionPreference = "Stop"

# Import Pester module
if (-not (Get-Module -ListAvailable -Name Pester)) {
    Write-Host "Installing Pester module..." -ForegroundColor Yellow
    Install-Module -Name Pester -Force -SkipPublisherCheck -Scope CurrentUser
}

Import-Module Pester -Force

Write-Host "🧪 Running Swickard ADF Tests - Category: $Category" -ForegroundColor Green

$testResults = @()
$totalTests = 0
$passedTests = 0
$failedTests = 0

# Define test categories
$testCategories = @{
    "Validation" = @{
        Path = "deployment/validate.ps1"
        Description = "ADF artifact validation"
        Type = "Script"
    }
    "Unit" = @{
        Path = "$TestPath/unit"
        Description = "Unit tests for ADF artifacts"
        Type = "Pester"
    }
    "Integration" = @{
        Path = "$TestPath/integration"
        Description = "Integration tests"
        Type = "Pester"
    }
    "Performance" = @{
        Path = "$TestPath/performance"
        Description = "Performance and load tests"
        Type = "Pester"
    }
}

# Function to run validation tests
function Invoke-ValidationTests {
    Write-Host "`n🔍 Running Validation Tests..." -ForegroundColor Cyan
    
    try {
        & "./deployment/validate.ps1" -Verbose:$Verbose
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Validation tests passed" -ForegroundColor Green
            return @{ Passed = $true; Message = "Validation successful" }
        } else {
            Write-Host "❌ Validation tests failed" -ForegroundColor Red
            return @{ Passed = $false; Message = "Validation failed" }
        }
    }
    catch {
        Write-Host "❌ Validation tests error: $($_.Exception.Message)" -ForegroundColor Red
        return @{ Passed = $false; Message = $_.Exception.Message }
    }
}

# Function to run Pester tests
function Invoke-PesterTests {
    param(
        [string]$Path,
        [string]$Description
    )
    
    if (-not (Test-Path $Path)) {
        Write-Host "⚠️  Test path not found: $Path" -ForegroundColor Yellow
        return @{ Passed = $true; Message = "No tests found"; Tests = 0 }
    }
    
    Write-Host "`n🧪 Running $Description..." -ForegroundColor Cyan
    
    $pesterConfig = New-PesterConfiguration
    $pesterConfig.Run.Path = $Path
    $pesterConfig.Output.Verbosity = if ($Verbose) { 'Detailed' } else { 'Normal' }
    $pesterConfig.CodeCoverage.Enabled = $GenerateReport
    $pesterConfig.TestResult.Enabled = $GenerateReport
    $pesterConfig.TestResult.OutputPath = "TestResults-$($Description -replace ' ', '').xml"
    
    try {
        $result = Invoke-Pester -Configuration $pesterConfig
        
        $script:totalTests += $result.TotalCount
        $script:passedTests += $result.PassedCount
        $script:failedTests += $result.FailedCount
        
        if ($result.FailedCount -eq 0) {
            Write-Host "✅ $Description passed ($($result.PassedCount)/$($result.TotalCount))" -ForegroundColor Green
            return @{ 
                Passed = $true
                Message = "$($result.PassedCount)/$($result.TotalCount) tests passed"
                Tests = $result.TotalCount
            }
        } else {
            Write-Host "❌ $Description failed ($($result.FailedCount)/$($result.TotalCount) failed)" -ForegroundColor Red
            return @{ 
                Passed = $false
                Message = "$($result.FailedCount)/$($result.TotalCount) tests failed"
                Tests = $result.TotalCount
            }
        }
    }
    catch {
        Write-Host "❌ $Description error: $($_.Exception.Message)" -ForegroundColor Red
        return @{ Passed = $false; Message = $_.Exception.Message; Tests = 0 }
    }
}

# Run tests based on category
if ($Category -eq "All") {
    foreach ($cat in $testCategories.Keys) {
        $categoryInfo = $testCategories[$cat]
        
        if ($categoryInfo.Type -eq "Script") {
            $result = Invoke-ValidationTests
        } else {
            $result = Invoke-PesterTests -Path $categoryInfo.Path -Description $categoryInfo.Description
        }
        
        $testResults += @{
            Category = $cat
            Result = $result
        }
    }
} else {
    if ($testCategories.ContainsKey($Category)) {
        $categoryInfo = $testCategories[$Category]
        
        if ($categoryInfo.Type -eq "Script") {
            $result = Invoke-ValidationTests
        } else {
            $result = Invoke-PesterTests -Path $categoryInfo.Path -Description $categoryInfo.Description
        }
        
        $testResults += @{
            Category = $Category
            Result = $result
        }
    } else {
        Write-Host "❌ Unknown test category: $Category" -ForegroundColor Red
        exit 1
    }
}

# Generate summary report
Write-Host "`n📊 Test Summary Report" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Gray

$overallPassed = $true
foreach ($testResult in $testResults) {
    $status = if ($testResult.Result.Passed) { "✅ PASSED" } else { "❌ FAILED" }
    $color = if ($testResult.Result.Passed) { "Green" } else { "Red" }
    
    Write-Host "$($testResult.Category): $status - $($testResult.Result.Message)" -ForegroundColor $color
    
    if (-not $testResult.Result.Passed) {
        $overallPassed = $false
    }
}

Write-Host "`nOverall Statistics:" -ForegroundColor Cyan
Write-Host "  Total Tests: $totalTests" -ForegroundColor Gray
Write-Host "  Passed: $passedTests" -ForegroundColor Green
Write-Host "  Failed: $failedTests" -ForegroundColor Red

if ($GenerateReport) {
    Write-Host "`n📄 Test reports generated in TestResults-*.xml files" -ForegroundColor Yellow
}

# Exit with appropriate code
if ($overallPassed) {
    Write-Host "`n🎉 All tests passed!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "`n❌ Some tests failed!" -ForegroundColor Red
    exit 1
}
