{"json.schemas": [{"fileMatch": ["**/pipeline/*.json"], "url": "https://schema.management.azure.com/schemas/2018-06-01/Microsoft.DataFactory.json#/definitions/Pipeline"}, {"fileMatch": ["**/dataset/*.json"], "url": "https://schema.management.azure.com/schemas/2018-06-01/Microsoft.DataFactory.json#/definitions/Dataset"}, {"fileMatch": ["**/linkedService/*.json"], "url": "https://schema.management.azure.com/schemas/2018-06-01/Microsoft.DataFactory.json#/definitions/LinkedService"}, {"fileMatch": ["**/dataflow/*.json"], "url": "https://schema.management.azure.com/schemas/2018-06-01/Microsoft.DataFactory.json#/definitions/MappingDataFlow"}, {"fileMatch": ["**/trigger/*.json"], "url": "https://schema.management.azure.com/schemas/2018-06-01/Microsoft.DataFactory.json#/definitions/Trigger"}], "files.associations": {"*.json": "jsonc"}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "editor.tabSize": 2, "editor.insertSpaces": true, "files.exclude": {"**/bin": true, "**/obj": true, "**/.azure": true, "**/node_modules": true, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "search.exclude": {"**/bin": true, "**/obj": true, "**/node_modules": true, "**/.git": true}, "powershell.codeFormatting.preset": "OTBS", "powershell.integratedConsole.showOnStartup": false, "azure.tenant": "", "azure.cloud": "AzureCloud", "git.ignoreLimitWarning": true, "files.watcherExclude": {"**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/node_modules/**": true, "**/bin/**": true, "**/obj/**": true}}