{"version": "2.0.0", "tasks": [{"label": "Validate ADF Artifacts", "type": "shell", "command": "powershell", "args": ["-File", "./deployment/validate.ps1"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Deploy to Dev", "type": "shell", "command": "powershell", "args": ["-File", "./deployment/deploy.ps1", "-Environment", "dev"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "dependsOn": "Validate ADF Artifacts"}, {"label": "Deploy to Test", "type": "shell", "command": "powershell", "args": ["-File", "./deployment/deploy.ps1", "-Environment", "test"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "dependsOn": "Validate ADF Artifacts"}, {"label": "Run Unit Tests", "type": "shell", "command": "powershell", "args": ["-File", "./tests/run-tests.ps1", "-Category", "Unit"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Run Integration Tests", "type": "shell", "command": "powershell", "args": ["-File", "./tests/run-tests.ps1", "-Category", "Integration"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Setup Development Environment", "type": "shell", "command": "powershell", "args": ["-File", "./deployment/setup-dev-environment.ps1"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared"}}, {"label": "Start Local Services", "type": "shell", "command": "docker-compose", "args": ["up", "-d"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Stop Local Services", "type": "shell", "command": "docker-compose", "args": ["down"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}