# Data Sources and Function Apps Configuration - Swickard EDW

## 🏗️ **Current Architecture Overview**

Based on analysis of your live production SwickardEDW Data Factory, here's the complete data source and function app configuration:

## 🔗 **Configured Linked Services**

### **1. Data Storage Services**

#### **LS_ADLS_Sagedw** (Primary Data Lake)
```json
{
  "type": "AzureBlobFS",
  "url": "https://sagedw.dfs.core.windows.net/",
  "connectVia": "AutoResolveIR-ManagedVNet",
  "authentication": "Managed Identity"
}
```
- **Purpose**: Primary data lake storage for landing and bronze layers
- **Integration Runtime**: Managed VNet for secure connectivity
- **Storage Account**: `sagedw` (Azure Data Lake Storage Gen2)

#### **LS_SQL_StgReynolds** (Staging Database)
- **Purpose**: SQL staging database for processed data
- **Layer**: Bronze → Silver transformations
- **Contains**: All Silver_* tables (Customer, Vehicle, Deal, etc.)

#### **AzureSqlDatabaseEDW** (Enterprise Data Warehouse)
- **Purpose**: Final data warehouse for analytics
- **Layer**: Gold/Presentation layer

### **2. Function App Services**

#### **LS_Func_SwickardETL** (Primary ETL Function)
```json
{
  "type": "AzureFunction",
  "functionAppUrl": "https://func-swickard-etl-v2.azurewebsites.net",
  "authentication": "Anonymous",
  "encryptedCredential": "Secured"
}
```
- **Function Name**: `FTPETLPipeline`
- **Purpose**: Primary ETL processing for landing → bronze
- **Trigger**: Called by `PL_Bronze` pipeline
- **Version**: v2 (indicates recent updates)

#### **LS_Func_CsvProcessing** (CSV Processing Function)
```json
{
  "type": "AzureFunction", 
  "functionAppUrl": "https://csv-processing-func-3514.azurewebsites.net",
  "authentication": "Anonymous"
}
```
- **Function Name**: `CSVProcessingPipeline`
- **Purpose**: Specialized CSV file processing
- **Trigger**: Sequential after SwickardETL function
- **Pattern**: Microservice architecture for specific data types

## 📊 **Data Flow Architecture**

### **Landing → Bronze → Silver Pipeline Flow:**

```mermaid
graph TD
    A[External Data Sources] -->|Landing| B[sagedw Data Lake]
    B -->|PL_Bronze| C[func-swickard-etl-v2]
    C -->|FTPETLPipeline| D[Bronze Layer Processing]
    D -->|Sequential| E[csv-processing-func-3514]
    E -->|CSVProcessingPipeline| F[Processed Bronze Data]
    F -->|PL_Silver| G[LS_SQL_StgReynolds Database]
    G --> H[Silver_* Tables]
    
    I[Reynolds XML] -->|pl_ingest_SalesLead| J[ds_sql_raw_reynolds_SalesLeads]
    J -->|Direct Load| G
```

### **Function App Execution Sequence:**
```yaml
PL_Bronze Pipeline:
  Step 1: Call_FuncApp1_ETL
    - Function: func-swickard-etl-v2/FTPETLPipeline
    - Method: POST
    - Retry: 2 attempts, 30s intervals
    - Timeout: 12 minutes
    
  Step 2: Call_FuncApp2_Process (depends on Step 1 success)
    - Function: csv-processing-func-3514/CSVProcessingPipeline  
    - Method: POST
    - Retry: 2 attempts, 30s intervals
    - Timeout: 12 minutes
```

## 🗂️ **Data Source Analysis**

### **Identified Data Sources:**

#### **1. Reynolds DMS System**
- **Format**: XML files
- **Landing Path**: `reynolds/saleslead/{year}/{month}/{day}/`
- **Sample File**: `PublishSalesLeadDataSample1.xml`
- **Processing**: Direct ingestion via `pl_ingest_SalesLead`
- **Target**: `ds_sql_raw_reynolds_SalesLeads`

#### **2. FTP-Based Sources** (via func-swickard-etl-v2)
- **Function**: `FTPETLPipeline` suggests FTP data retrieval
- **Processing**: Landing → Bronze transformation
- **Storage**: `sagedw` data lake

#### **3. CSV File Sources** (via csv-processing-func-3514)
- **Format**: CSV files
- **Processing**: Specialized CSV parsing and validation
- **Integration**: Sequential processing after main ETL

#### **4. Bronze Layer Files**
- **Examples Found**:
  - `SampleFIMAST_bronzefile_parquet`
  - `SampleCUSTOMER_ACCTG_bronzefile_parquet`
- **Format**: Parquet files for optimized processing
- **Pattern**: Structured bronze layer with business entity separation

## 🔧 **Function App Capabilities Analysis**

### **func-swickard-etl-v2 Capabilities:**
Based on naming and integration patterns:

```python
# Inferred capabilities:
class SwickardETLFunction:
    def FTPETLPipeline(self):
        """
        Primary ETL function likely handles:
        - FTP server connections for data retrieval
        - File format detection and parsing
        - Data validation and cleansing
        - Landing → Bronze layer transformation
        - Error handling and logging
        """
        pass
```

**Likely Data Sources:**
- FTP servers with automotive industry data
- Vendor data feeds (parts, inventory, sales)
- Third-party integrations (financing, insurance)

### **csv-processing-func-3514 Capabilities:**
```python
# Inferred capabilities:
class CSVProcessingFunction:
    def CSVProcessingPipeline(self):
        """
        Specialized CSV processing likely handles:
        - CSV schema validation
        - Data type conversion and formatting
        - Duplicate detection and handling
        - Business rule validation
        - Bronze layer optimization (Parquet conversion)
        """
        pass
```

## 🚨 **Configuration Analysis & Recommendations**

### **Current Strengths:**
✅ **Microservice Architecture**: Separate functions for different data types
✅ **Retry Logic**: 2 retries with 30s intervals for resilience
✅ **Sequential Processing**: Proper dependency management
✅ **Managed VNet**: Secure connectivity for data lake access
✅ **Version Control**: v2 naming suggests iterative improvements

### **Potential Issues Identified:**

#### **1. Anonymous Authentication**
```json
"authentication": "Anonymous"  // ← Security concern
```
**Risk**: Function apps accessible without authentication
**Recommendation**: Implement function key or managed identity authentication

#### **2. Short Timeout Windows**
```json
"timeout": "0.12:00:00"  // ← Only 12 minutes
```
**Risk**: Large data processing jobs may timeout
**Recommendation**: Increase timeout for data-intensive operations

#### **3. Limited Error Handling**
**Current**: Basic retry logic only
**Recommendation**: Implement dead letter queues and comprehensive error logging

## 📈 **Optimization Opportunities**

### **1. Parallel Processing**
```yaml
# Current: Sequential processing
FTPETLPipeline → CSVProcessingPipeline

# Recommended: Parallel processing where possible
FTPETLPipeline ↘
                → Merge Results → Silver Layer
CSVProcessingPipeline ↗
```

### **2. Enhanced Monitoring**
```python
# Add custom metrics to function apps:
- Processing duration by data source
- Record counts and data quality metrics
- Error rates and retry patterns
- Data freshness indicators
```

### **3. Configuration Management**
```json
// Parameterize function app configurations:
{
  "functionApps": {
    "etl": {
      "url": "#{ETL_FUNCTION_URL}#",
      "timeout": "#{ETL_TIMEOUT}#",
      "retries": "#{ETL_RETRIES}#"
    }
  }
}
```

## 🔍 **Data Lineage Summary**

### **Complete Data Flow:**
```
External Sources (FTP, Reynolds, CSV)
    ↓
Landing Zone (sagedw Data Lake)
    ↓
func-swickard-etl-v2 (FTPETLPipeline)
    ↓
csv-processing-func-3514 (CSVProcessingPipeline)
    ↓
Bronze Layer (Parquet files)
    ↓
Silver Layer (SQL Database - LS_SQL_StgReynolds)
    ↓
Gold Layer (AzureSqlDatabaseEDW)
```

### **Key Integration Points:**
- **Reynolds XML**: Direct ingestion bypass for real-time sales leads
- **FTP Sources**: Batch processing through function apps
- **CSV Files**: Specialized processing for structured data
- **Bronze Optimization**: Parquet format for performance

## 🎯 **Next Steps for Local Development**

### **1. Function App Analysis**
- Export function app code for local development
- Analyze data source connections and credentials
- Document business logic and transformation rules

### **2. Data Source Mapping**
- Catalog all FTP sources and connection details
- Document CSV file schemas and validation rules
- Map Reynolds XML structure to business entities

### **3. Testing Strategy**
- Create sample data sets for each source type
- Implement unit tests for function app logic
- Set up integration testing with mock data sources

---

*This analysis reveals a sophisticated multi-layer data architecture with specialized function apps for different data source types. The microservice approach provides flexibility but requires careful orchestration and monitoring.*
