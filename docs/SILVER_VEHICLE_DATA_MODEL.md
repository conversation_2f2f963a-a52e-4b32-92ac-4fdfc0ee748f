# Silver Vehicle Data Model Structure - Swickard EDW

## 🚗 **Vehicle Data Architecture Overview**

Based on analysis of your live production SwickardEDW Data Factory, here's the complete Silver layer vehicle data model structure:

## 📊 **Core Vehicle Tables**

### **1. Silver_Vehicle** (Hub Table)
- **Purpose**: Central vehicle entity with core identifiers
- **Location**: `dbo.Silver_Vehicle` in SQL Database
- **Linked Service**: `LS_SQL_StgReynolds`
- **Type**: Hub table in dimensional model

**Expected Fields** (based on sales lead mapping analysis):
```sql
-- Core vehicle identifiers
VehicleID (Primary Key)
VIN (Unique Identifier)
StockNumber
VehicleKey (Business Key)

-- Basic vehicle attributes  
VehicleYear
VehicleMake
VehicleModel
VehicleStyle
StockType (New/Used)

-- Audit fields
CreatedDate
ModifiedDate
SourceSystem
```

### **2. Silver_VehicleSaleDetail** (Sales Context)
- **Purpose**: Vehicle sales-specific information
- **Relationship**: One-to-Many with Silver_Vehicle

**Expected Fields**:
```sql
VehicleSaleDetailID (Primary Key)
VehicleID (Foreign Key → Silver_Vehicle)
SalePrice
ListPrice
InvoicePrice
SaleDate
SalesPersonID
DealershipID
SaleStatus
FinancingType
```

### **3. Silver_VehicleReynoldsDetail** (Reynolds System Data)
- **Purpose**: Reynolds DMS-specific vehicle data
- **Source**: Reynolds XML feeds via `pl_ingest_SalesLead`

**Expected Fields**:
```sql
VehicleReynoldsDetailID (Primary Key)
VehicleID (Foreign Key → Silver_Vehicle)
ReynoldsVehicleID
DealerNumber
StoreNumber
AreaNumber
BusinessUnitName
ReynoldsStockNumber
ReynoldsStatus
LastSyncDate
```

### **4. Silver_VehicleAcquisitionDetail** (Acquisition Info)
- **Purpose**: How/when vehicle was acquired by dealership

**Expected Fields**:
```sql
VehicleAcquisitionDetailID (Primary Key)
VehicleID (Foreign Key → Silver_Vehicle)
AcquisitionDate
AcquisitionType (Trade-in, Auction, Manufacturer)
AcquisitionCost
AcquisitionSource
TradeInDealID (if trade-in)
AuctionDetails
```

## 🔗 **Relationship Tables**

### **5. Silver_DealVehicleLink** (Junction Table)
- **Purpose**: Links vehicles to deals (many-to-many relationship)
- **Pattern**: Bridge table for complex relationships

**Expected Fields**:
```sql
DealVehicleLinkID (Primary Key)
DealID (Foreign Key → Silver_Deal)
VehicleID (Foreign Key → Silver_Vehicle)
VehicleRole (Primary, Trade-in, Additional)
LinkDate
LinkStatus
```

## 🗺️ **Data Flow Mapping**

### **Source to Silver Flow:**
```mermaid
graph TD
    A[Reynolds XML Feed] -->|pl_ingest_SalesLead| B[ds_sql_raw_reynolds_SalesLeads]
    B -->|PL_Bronze| C[Bronze Layer Tables]
    C -->|PL_Silver| D[Silver_Vehicle]
    C -->|PL_Silver| E[Silver_VehicleReynoldsDetail]
    C -->|PL_Silver| F[Silver_VehicleSaleDetail]
    C -->|PL_Silver| G[Silver_VehicleAcquisitionDetail]
    
    H[Silver_Deal] -->|Many-to-Many| I[Silver_DealVehicleLink]
    D -->|Many-to-Many| I
```

### **Field Mapping from Sales Lead XML:**
Based on your `pl_ingest_SalesLead` pipeline analysis:

```json
// Vehicle fields extracted from Reynolds XML:
{
  "DesiredVehicle": {
    "StockType": "→ Silver_Vehicle.StockType",
    "Vin": "→ Silver_Vehicle.VIN", 
    "VehicleYear": "→ Silver_Vehicle.VehicleYear",
    "VehicleMake": "→ Silver_Vehicle.VehicleMake",
    "VehicleModel": "→ Silver_Vehicle.VehicleModel",
    "VehicleStyle": "→ Silver_Vehicle.VehicleStyle"
  },
  "PotentialTrade": {
    "TradeVehicleYear": "→ Silver_VehicleAcquisitionDetail.TradeYear",
    "TradeVehicleMake": "→ Silver_VehicleAcquisitionDetail.TradeMake", 
    "TradeVehicleModel": "→ Silver_VehicleAcquisitionDetail.TradeModel",
    "TradeVehicleOdometer": "→ Silver_VehicleAcquisitionDetail.TradeOdometer"
  }
}
```

## 🔍 **Data Relationships**

### **Primary Relationships:**
```sql
-- Vehicle to Deal (Many-to-Many via Link Table)
Silver_Vehicle.VehicleID ←→ Silver_DealVehicleLink.VehicleID
Silver_Deal.DealID ←→ Silver_DealVehicleLink.DealID

-- Vehicle to Detail Tables (One-to-Many)
Silver_Vehicle.VehicleID → Silver_VehicleSaleDetail.VehicleID
Silver_Vehicle.VehicleID → Silver_VehicleReynoldsDetail.VehicleID  
Silver_Vehicle.VehicleID → Silver_VehicleAcquisitionDetail.VehicleID

-- Customer to Vehicle (via Deal)
Silver_Customer → Silver_DealCustomerLink → Silver_Deal → Silver_DealVehicleLink → Silver_Vehicle
```

## 🎯 **Business Logic Insights**

### **Vehicle Lifecycle in Your System:**
1. **Lead Generation**: Vehicle interest captured in sales lead XML
2. **Inventory Management**: Vehicle details stored in Silver_Vehicle
3. **Sales Process**: Vehicle linked to deals via Silver_DealVehicleLink
4. **Transaction**: Sale details recorded in Silver_VehicleSaleDetail
5. **Trade Processing**: Trade-in vehicles processed through acquisition detail

### **Key Business Rules** (inferred from structure):
- **VIN Uniqueness**: Each vehicle has unique VIN identifier
- **Multi-Deal Support**: Vehicles can be involved in multiple deals (trade-ins, re-sales)
- **Reynolds Integration**: All vehicles sync with Reynolds DMS system
- **Audit Trail**: Full tracking of vehicle acquisition and sale history

## 🚨 **Potential Data Quality Issues**

Based on the XML mapping analysis, watch for:

### **1. VIN Validation**
```sql
-- Check for invalid VINs
SELECT VehicleID, VIN 
FROM Silver_Vehicle 
WHERE LEN(VIN) != 17 OR VIN IS NULL;
```

### **2. Year Validation**
```sql
-- Check for unrealistic vehicle years
SELECT VehicleID, VehicleYear
FROM Silver_Vehicle 
WHERE VehicleYear < 1900 OR VehicleYear > YEAR(GETDATE()) + 2;
```

### **3. Orphaned Records**
```sql
-- Check for vehicles without Reynolds details
SELECT sv.VehicleID, sv.VIN
FROM Silver_Vehicle sv
LEFT JOIN Silver_VehicleReynoldsDetail svrd ON sv.VehicleID = svrd.VehicleID
WHERE svrd.VehicleReynoldsDetailID IS NULL;
```

## 🔧 **AI-Powered Analysis Opportunities**

### **1. Data Lineage Mapping**
- Trace vehicle data from XML source through all transformations
- Identify data quality issues in the pipeline
- Map field-level lineage for compliance

### **2. Performance Optimization**
- Analyze join patterns between vehicle tables
- Suggest indexing strategies for common queries
- Identify denormalization opportunities

### **3. Business Intelligence**
- Vehicle inventory turnover analysis
- Trade-in pattern recognition
- Sales performance by vehicle attributes

## 📈 **Recommended Enhancements**

### **1. Add Data Validation**
```sql
-- Suggested constraints
ALTER TABLE Silver_Vehicle 
ADD CONSTRAINT CK_VIN_Length CHECK (LEN(VIN) = 17);

ALTER TABLE Silver_Vehicle
ADD CONSTRAINT CK_Vehicle_Year CHECK (VehicleYear BETWEEN 1900 AND YEAR(GETDATE()) + 2);
```

### **2. Improve Indexing**
```sql
-- Suggested indexes for performance
CREATE INDEX IX_Silver_Vehicle_VIN ON Silver_Vehicle(VIN);
CREATE INDEX IX_Silver_Vehicle_Make_Model ON Silver_Vehicle(VehicleMake, VehicleModel);
CREATE INDEX IX_DealVehicleLink_Composite ON Silver_DealVehicleLink(DealID, VehicleID);
```

### **3. Add Audit Triggers**
```sql
-- Track changes to vehicle records
CREATE TRIGGER tr_Silver_Vehicle_Audit 
ON Silver_Vehicle 
AFTER UPDATE, DELETE
AS
-- Audit logic here
```

---

*This analysis was generated by examining your live production SwickardEDW Data Factory structure and inferring relationships based on naming conventions and data flow patterns.*
