# Pipeline Orchestration - Swickard EDW

## 🔄 **Data Factory Pipeline Flow**

This diagram shows the detailed orchestration of Azure Data Factory pipelines, including dependencies, error handling, and execution flow.

```mermaid
graph TD
    %% Triggers and Scheduling
    subgraph "Triggers & Scheduling"
        T1[Schedule Trigger<br/>Daily 2:00 AM]
        T2[Event Trigger<br/>File Arrival]
        T3[Manual Trigger<br/>On-Demand]
    end
    
    %% Sales Lead Pipeline (Real-time)
    subgraph "Real-Time Sales Lead Processing"
        SL1[pl_ingest_SalesLead<br/>Pipeline Start]
        SL2{XML File<br/>Available?}
        SL3[Copy Activity<br/>XML → SQL]
        SL4[Data Validation<br/>37 Field Mappings]
        SL5[Load to<br/>ds_sql_raw_reynolds_SalesLeads]
        SL6[Success<br/>Notification]
        SL7[Error<br/>Handling]
    end
    
    %% Bronze Pipeline (Batch Processing)
    subgraph "Bronze Layer Processing"
        B1[PL_Bronze<br/>Pipeline Start]
        B2[Call_FuncApp1_ETL<br/>Azure Function Activity]
        B3{FTP ETL<br/>Success?}
        B4[Call_FuncApp2_Process<br/>CSV Processing]
        B5{CSV Processing<br/>Success?}
        B6[Bronze Layer<br/>Complete]
        B7[Error Retry<br/>2 attempts, 30s interval]
        B8[Pipeline<br/>Failure]
    end
    
    %% Silver Pipeline (Transformation)
    subgraph "Silver Layer Processing"
        S1[PL_Silver<br/>Pipeline Start]
        S2[Data Quality<br/>Validation]
        S3{Quality<br/>Checks Pass?}
        S4[Transform to<br/>Business Entities]
        S5[Load Customer<br/>Hub & Satellites]
        S6[Load Vehicle<br/>Hub & Satellites]
        S7[Load Deal<br/>Hub & Satellites]
        S8[Create Link<br/>Tables]
        S9[Silver Layer<br/>Complete]
        S10[Data Quality<br/>Failure]
    end
    
    %% Function App Details
    subgraph "Function App Execution"
        F1[func-swickard-etl-v2<br/>FTPETLPipeline]
        F2[FTP Connection<br/>& Data Retrieval]
        F3[File Processing<br/>& Validation]
        F4[Landing → Bronze<br/>Transformation]
        F5[csv-processing-func-3514<br/>CSVProcessingPipeline]
        F6[CSV Schema<br/>Validation]
        F7[Data Type<br/>Conversion]
        F8[Parquet File<br/>Generation]
    end
    
    %% Error Handling & Monitoring
    subgraph "Error Handling & Monitoring"
        E1[Pipeline Failure<br/>Detection]
        E2[Retry Logic<br/>Exponential Backoff]
        E3[Dead Letter<br/>Queue]
        E4[Alert<br/>Notifications]
        E5[Log Analytics<br/>Workspace]
        E6[Monitoring<br/>Dashboard]
    end
    
    %% Trigger Connections
    T1 --> B1
    T2 --> SL1
    T3 --> B1
    T3 --> SL1
    T3 --> S1
    
    %% Sales Lead Flow
    SL1 --> SL2
    SL2 -->|Yes| SL3
    SL2 -->|No| SL7
    SL3 --> SL4
    SL4 --> SL5
    SL5 --> SL6
    SL4 -->|Validation Error| SL7
    SL3 -->|Copy Error| SL7
    
    %% Bronze Pipeline Flow
    B1 --> B2
    B2 --> B3
    B3 -->|Success| B4
    B3 -->|Failure| B7
    B4 --> B5
    B5 -->|Success| B6
    B5 -->|Failure| B7
    B7 -->|Retry Exhausted| B8
    B7 -->|Retry| B2
    
    %% Silver Pipeline Flow
    B6 --> S1
    SL6 --> S1
    S1 --> S2
    S2 --> S3
    S3 -->|Pass| S4
    S3 -->|Fail| S10
    S4 --> S5
    S5 --> S6
    S6 --> S7
    S7 --> S8
    S8 --> S9
    
    %% Function App Flow
    B2 --> F1
    F1 --> F2
    F2 --> F3
    F3 --> F4
    B4 --> F5
    F5 --> F6
    F6 --> F7
    F7 --> F8
    
    %% Error Handling Flow
    SL7 --> E1
    B8 --> E1
    S10 --> E1
    E1 --> E2
    E1 --> E4
    E2 --> E3
    E4 --> E5
    E5 --> E6
    
    %% Styling
    classDef triggerStyle fill:#e3f2fd,stroke:#0277bd,stroke-width:2px
    classDef salesStyle fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef bronzeStyle fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef silverStyle fill:#e0f2f1,stroke:#00796b,stroke-width:2px
    classDef functionStyle fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef errorStyle fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef decisionStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class T1,T2,T3 triggerStyle
    class SL1,SL3,SL4,SL5,SL6 salesStyle
    class B1,B2,B4,B6 bronzeStyle
    class S1,S2,S4,S5,S6,S7,S8,S9 silverStyle
    class F1,F2,F3,F4,F5,F6,F7,F8 functionStyle
    class SL7,B7,B8,S10,E1,E2,E3,E4,E5,E6 errorStyle
    class SL2,B3,B5,S3 decisionStyle
```

## ⚙️ **Pipeline Execution Details**

### **1. Sales Lead Pipeline (pl_ingest_SalesLead)**
```yaml
Trigger: Event-based (file arrival)
Frequency: Real-time
Timeout: 12 minutes
Retry Policy: 0 retries (immediate failure)
Dependencies: None

Activities:
  - Copy Activity: XML → SQL transformation
  - Field Mappings: 37 complex XML path mappings
  - Target: ds_sql_raw_reynolds_SalesLeads
  - Validation: Data type and format checks
```

### **2. Bronze Pipeline (PL_Bronze)**
```yaml
Trigger: Scheduled (daily) + Manual
Frequency: Daily at 2:00 AM
Timeout: 12 minutes per activity
Retry Policy: 2 retries, 30-second intervals
Dependencies: Data availability

Activities:
  - Azure Function Activity 1: FTPETLPipeline
  - Azure Function Activity 2: CSVProcessingPipeline (sequential)
  - Error Handling: Comprehensive retry logic
  - Monitoring: Custom metrics and logging
```

### **3. Silver Pipeline (PL_Silver)**
```yaml
Trigger: Success of Bronze + Sales Lead pipelines
Frequency: After source pipeline completion
Timeout: Variable based on data volume
Retry Policy: Business logic dependent
Dependencies: Bronze layer completion

Activities:
  - Data Quality Validation
  - Business Entity Transformation
  - Hub Table Loading (Customer, Vehicle, Deal)
  - Satellite Table Loading (Detail tables)
  - Link Table Creation (Junction tables)
```

## 🔄 **Execution Patterns**

### **Sequential Processing Pattern**
```mermaid
sequenceDiagram
    participant T as Trigger
    participant B as Bronze Pipeline
    participant F1 as FTP Function
    participant F2 as CSV Function
    participant S as Silver Pipeline
    
    T->>B: Start Bronze Processing
    B->>F1: Execute FTPETLPipeline
    F1-->>B: Success Response
    B->>F2: Execute CSVProcessingPipeline
    F2-->>B: Success Response
    B->>S: Trigger Silver Pipeline
    S-->>B: Processing Complete
```

### **Parallel Processing Pattern**
```mermaid
sequenceDiagram
    participant T as Event Trigger
    participant SL as Sales Lead Pipeline
    participant B as Bronze Pipeline
    participant S as Silver Pipeline
    
    T->>SL: File Arrival Event
    T->>B: Scheduled Trigger
    
    par Sales Lead Processing
        SL->>SL: Process XML
    and Bronze Processing
        B->>B: Process Batch Data
    end
    
    SL->>S: Real-time Data Ready
    B->>S: Batch Data Ready
    S->>S: Merge & Transform
```

## 🚨 **Error Handling Strategy**

### **Retry Policies**
```yaml
Sales Lead Pipeline:
  - Retry Count: 0 (fail fast for real-time data)
  - Timeout: 12 minutes
  - Error Action: Immediate notification

Bronze Pipeline:
  - Retry Count: 2 per activity
  - Retry Interval: 30 seconds
  - Backoff: Linear
  - Error Action: Dead letter queue

Silver Pipeline:
  - Retry Count: 3 (data quality dependent)
  - Retry Interval: 60 seconds
  - Backoff: Exponential
  - Error Action: Rollback and alert
```

### **Monitoring & Alerting**
```yaml
Pipeline Monitoring:
  - Success/Failure rates
  - Execution duration trends
  - Data volume metrics
  - Error pattern analysis

Alerting Channels:
  - Email notifications
  - Teams/Slack integration
  - Azure Monitor alerts
  - Custom dashboard updates
```

## 📊 **Performance Metrics**

### **Current Performance Baselines**
- **Sales Lead Processing**: < 5 minutes per file
- **Bronze Pipeline**: 30-45 minutes for full batch
- **Silver Pipeline**: 15-30 minutes for transformation
- **End-to-End Latency**: < 2 hours for batch data

### **Optimization Opportunities**
1. **Parallel Function Execution**: Reduce bronze processing time by 40%
2. **Incremental Loading**: Implement delta processing for large tables
3. **Partition Optimization**: Improve query performance in silver layer
4. **Caching Strategy**: Reduce redundant data movement

---

*This orchestration design ensures reliable, scalable data processing while maintaining data quality and providing comprehensive error handling and monitoring capabilities.*
