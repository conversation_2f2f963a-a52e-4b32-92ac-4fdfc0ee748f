# Security Architecture - Swickard EDW

## 🔐 **Security & Compliance Architecture**

This diagram shows the comprehensive security architecture including authentication, authorization, data encryption, network security, and compliance controls.

```mermaid
graph TB
    %% Identity & Access Management
    subgraph "Identity & Access Management"
        AAD[Azure Active Directory<br/>Central Identity Provider]
        MI[Managed Identity<br/>Service Authentication]
        RBAC[Role-Based Access Control<br/>Granular Permissions]
        PIM[Privileged Identity Management<br/>Just-in-Time Access]
    end
    
    %% Network Security
    subgraph "Network Security Layer"
        VNET[Virtual Network<br/>Isolated Network Boundary]
        NSG[Network Security Groups<br/>Traffic Filtering Rules]
        PE[Private Endpoints<br/>Secure Service Access]
        FW[Azure Firewall<br/>Network Protection]
        MVNET[Managed VNet<br/>ADF Integration Runtime]
    end
    
    %% Data Security
    subgraph "Data Security & Encryption"
        TDE[Transparent Data Encryption<br/>SQL Database Encryption]
        CMK[Customer Managed Keys<br/>Azure Key Vault]
        ADLS[ADLS Gen2 Encryption<br/>Data Lake Security]
        SSL[SSL/TLS Encryption<br/>Data in Transit]
    end
    
    %% Application Security
    subgraph "Application Security"
        ADF[Azure Data Factory<br/>Managed VNet Integration]
        FUNC[Function Apps<br/>Anonymous Auth - Risk]
        SQL[Azure SQL Database<br/>AAD Authentication]
        KV[Azure Key Vault<br/>Secrets Management]
    end
    
    %% Monitoring & Compliance
    subgraph "Monitoring & Compliance"
        LOG[Log Analytics Workspace<br/>Centralized Logging]
        SEC[Azure Security Center<br/>Security Posture]
        SEN[Azure Sentinel<br/>SIEM & SOAR]
        POL[Azure Policy<br/>Compliance Enforcement]
        AUDIT[Audit Logs<br/>Activity Tracking]
    end
    
    %% Data Classification
    subgraph "Data Classification & Governance"
        PUR[Microsoft Purview<br/>Data Catalog & Lineage]
        DLP[Data Loss Prevention<br/>Sensitive Data Protection]
        RET[Retention Policies<br/>Data Lifecycle Management]
        GDPR[GDPR Compliance<br/>Privacy Controls]
    end
    
    %% External Connections
    subgraph "External Data Sources"
        REY[Reynolds DMS<br/>XML over HTTPS]
        FTP[FTP Servers<br/>Secure FTP/SFTP]
        API[Third-Party APIs<br/>OAuth 2.0/API Keys]
    end
    
    %% Security Flow Connections
    AAD --> MI
    AAD --> RBAC
    AAD --> PIM
    MI --> ADF
    MI --> SQL
    MI --> ADLS
    
    VNET --> NSG
    VNET --> PE
    VNET --> MVNET
    NSG --> FW
    PE --> SQL
    PE --> ADLS
    MVNET --> ADF
    
    CMK --> TDE
    CMK --> ADLS
    KV --> CMK
    KV --> FUNC
    SSL --> REY
    SSL --> FTP
    SSL --> API
    
    ADF --> LOG
    FUNC --> LOG
    SQL --> LOG
    LOG --> SEC
    LOG --> SEN
    POL --> ADF
    POL --> SQL
    AUDIT --> LOG
    
    PUR --> ADLS
    PUR --> SQL
    DLP --> ADLS
    RET --> ADLS
    GDPR --> DLP
    
    %% Styling
    classDef identityStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef networkStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef dataStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef appStyle fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef monitorStyle fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef governStyle fill:#e0f2f1,stroke:#00796b,stroke-width:2px
    classDef externalStyle fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    
    class AAD,MI,RBAC,PIM identityStyle
    class VNET,NSG,PE,FW,MVNET networkStyle
    class TDE,CMK,ADLS,SSL dataStyle
    class ADF,FUNC,SQL,KV appStyle
    class LOG,SEC,SEN,POL,AUDIT monitorStyle
    class PUR,DLP,RET,GDPR governStyle
    class REY,FTP,API externalStyle
```

## 🛡️ **Security Control Matrix**

### **Authentication & Authorization**

#### **Current Implementation**
```yaml
Azure Data Factory:
  Authentication: Managed Identity ✅
  Authorization: RBAC with custom roles ✅
  Network: Managed VNet integration ✅

Function Apps:
  Authentication: Anonymous ❌ (Security Risk)
  Authorization: No access control ❌
  Network: Public endpoint ❌

Azure SQL Database:
  Authentication: AAD + SQL Authentication ✅
  Authorization: Database-level RBAC ✅
  Network: Private endpoint ✅

Data Lake Storage:
  Authentication: Managed Identity ✅
  Authorization: ACL + RBAC ✅
  Network: Private endpoint ✅
```

#### **Security Recommendations**
```yaml
Immediate Actions Required:
  - Enable Function Key authentication for Function Apps
  - Implement VNet integration for Function Apps
  - Remove anonymous access from all services
  - Enable AAD-only authentication for SQL Database

Medium-term Improvements:
  - Implement Conditional Access policies
  - Enable Privileged Identity Management
  - Set up Azure AD Identity Protection
  - Implement Zero Trust network model
```

### **Data Protection & Encryption**

#### **Encryption at Rest**
```mermaid
graph LR
    subgraph "Data Lake Storage"
        A1[Raw Files<br/>AES-256 Encrypted]
        A2[Bronze Layer<br/>Parquet Encrypted]
        A3[Staging Data<br/>AES-256 Encrypted]
    end
    
    subgraph "SQL Database"
        B1[Silver Tables<br/>TDE Encrypted]
        B2[Gold Tables<br/>TDE Encrypted]
        B3[Backup Files<br/>Encrypted Backups]
    end
    
    subgraph "Key Management"
        C1[Azure Key Vault<br/>Customer Managed Keys]
        C2[Key Rotation<br/>Automated Policy]
        C3[Access Policies<br/>Least Privilege]
    end
    
    C1 --> A1
    C1 --> A2
    C1 --> A3
    C1 --> B1
    C1 --> B2
    C1 --> B3
    C2 --> C1
    C3 --> C1
```

#### **Encryption in Transit**
```yaml
Data Movement Security:
  Reynolds XML: HTTPS/TLS 1.2 ✅
  FTP Sources: SFTP/FTPS required ⚠️
  Function Apps: HTTPS only ✅
  Database Connections: TLS 1.2 encrypted ✅
  Internal ADF: Private network ✅

Certificate Management:
  - Automated certificate renewal
  - Strong cipher suites only
  - Perfect Forward Secrecy enabled
```

## 🔍 **Compliance & Governance**

### **GDPR Compliance Architecture**

```mermaid
graph TD
    %% Data Subject Rights
    subgraph "Data Subject Rights"
        DSR1[Right to Access<br/>Customer Data Export]
        DSR2[Right to Rectification<br/>Data Correction Process]
        DSR3[Right to Erasure<br/>Data Deletion Process]
        DSR4[Right to Portability<br/>Data Export Format]
    end
    
    %% Consent Management
    subgraph "Consent Management"
        CM1[Silver_CustomerOptOut<br/>Consent Tracking]
        CM2[Consent Audit Trail<br/>Change History]
        CM3[Consent Validation<br/>Business Rules]
        CM4[Consent Reporting<br/>Compliance Reports]
    end
    
    %% Data Processing
    subgraph "Data Processing Controls"
        DP1[Purpose Limitation<br/>Business Justification]
        DP2[Data Minimization<br/>Only Required Fields]
        DP3[Retention Policies<br/>Automated Deletion]
        DP4[Processing Records<br/>Article 30 Compliance]
    end
    
    %% Technical Safeguards
    subgraph "Technical Safeguards"
        TS1[Pseudonymization<br/>Customer Key Hashing]
        TS2[Access Controls<br/>Role-Based Permissions]
        TS3[Audit Logging<br/>All Data Access]
        TS4[Data Breach Detection<br/>Automated Monitoring]
    end
    
    DSR1 --> CM1
    DSR2 --> CM2
    DSR3 --> CM3
    DSR4 --> CM4
    
    CM1 --> DP1
    CM2 --> DP2
    CM3 --> DP3
    CM4 --> DP4
    
    DP1 --> TS1
    DP2 --> TS2
    DP3 --> TS3
    DP4 --> TS4
```

### **Data Classification & Sensitivity**

#### **Sensitivity Labels**
```yaml
Highly Confidential:
  - Customer PII (Names, Addresses, Phone Numbers)
  - Financial Information (Credit Scores, Income)
  - Vehicle VIN Numbers
  - Deal Financial Details

Confidential:
  - Customer Preferences
  - Vehicle Inventory Data
  - Sales Performance Metrics
  - Operational Reports

Internal:
  - Aggregated Analytics
  - Business Intelligence Reports
  - System Configuration Data
  - Audit Logs

Public:
  - Marketing Materials
  - Public Financial Reports
  - General Company Information
```

#### **Data Handling Requirements**
```sql
-- Example: PII Data Masking for Non-Production
CREATE VIEW vw_Customer_Masked AS
SELECT 
    CustomerID,
    CustomerKey,
    CASE 
        WHEN SYSTEM_USER IN ('DataAnalyst', 'BusinessUser') 
        THEN LEFT(FirstName, 1) + '***'
        ELSE FirstName 
    END AS FirstName,
    CASE 
        WHEN SYSTEM_USER IN ('DataAnalyst', 'BusinessUser')
        THEN LEFT(LastName, 1) + '***'
        ELSE LastName 
    END AS LastName,
    -- Additional masking logic
FROM Silver_Person;
```

## 🚨 **Security Monitoring & Incident Response**

### **Security Monitoring Dashboard**

```mermaid
graph TB
    %% Data Sources
    subgraph "Security Data Sources"
        SD1[Azure Activity Logs<br/>Administrative Actions]
        SD2[ADF Pipeline Logs<br/>Data Processing Events]
        SD3[SQL Audit Logs<br/>Database Access]
        SD4[Function App Logs<br/>Processing Events]
        SD5[Network Flow Logs<br/>Traffic Analysis]
    end
    
    %% Analytics & Detection
    subgraph "Security Analytics"
        SA1[Anomaly Detection<br/>Unusual Access Patterns]
        SA2[Threat Intelligence<br/>Known Bad Actors]
        SA3[Behavioral Analytics<br/>User Activity Baseline]
        SA4[Data Exfiltration Detection<br/>Large Data Movements]
    end
    
    %% Response & Remediation
    subgraph "Incident Response"
        IR1[Automated Response<br/>Block Suspicious Activity]
        IR2[Alert Notifications<br/>Security Team Alerts]
        IR3[Investigation Playbooks<br/>Standardized Response]
        IR4[Remediation Actions<br/>Containment & Recovery]
    end
    
    SD1 --> SA1
    SD2 --> SA2
    SD3 --> SA3
    SD4 --> SA4
    SD5 --> SA1
    
    SA1 --> IR1
    SA2 --> IR2
    SA3 --> IR3
    SA4 --> IR4
```

### **Security Metrics & KPIs**

#### **Security Posture Metrics**
```yaml
Authentication Metrics:
  - Failed login attempts: < 5% of total attempts
  - MFA adoption rate: > 95% for privileged accounts
  - Service account usage: Monitored and justified

Access Control Metrics:
  - Privileged access reviews: Monthly
  - Unused permissions: Quarterly cleanup
  - Access certification: 100% completion rate

Data Protection Metrics:
  - Encryption coverage: 100% of sensitive data
  - Key rotation compliance: 100% automated
  - Data classification accuracy: > 95%

Incident Response Metrics:
  - Mean time to detection: < 15 minutes
  - Mean time to response: < 1 hour
  - Mean time to resolution: < 4 hours
```

## 🎯 **Security Roadmap**

### **Phase 1: Critical Security Fixes (Immediate)**
```yaml
Week 1-2:
  - ❌ Remove anonymous authentication from Function Apps
  - ✅ Implement Function Key authentication
  - ✅ Enable HTTPS-only for all services
  - ✅ Configure private endpoints for Function Apps

Week 3-4:
  - ✅ Implement Azure AD authentication for all services
  - ✅ Configure Conditional Access policies
  - ✅ Enable audit logging for all data access
  - ✅ Set up security monitoring alerts
```

### **Phase 2: Enhanced Security Controls (1-3 months)**
```yaml
Month 1:
  - Implement Zero Trust network architecture
  - Deploy Azure Sentinel for SIEM capabilities
  - Configure data loss prevention policies
  - Establish security incident response procedures

Month 2-3:
  - Implement customer-managed encryption keys
  - Deploy Microsoft Purview for data governance
  - Configure automated compliance reporting
  - Establish security awareness training program
```

### **Phase 3: Advanced Security Features (3-6 months)**
```yaml
Advanced Capabilities:
  - Machine learning-based threat detection
  - Advanced data classification and labeling
  - Automated security remediation
  - Continuous compliance monitoring
  - Security orchestration and automation
```

---

*This security architecture provides comprehensive protection for the Swickard EDW while ensuring compliance with industry regulations and data privacy requirements.*
