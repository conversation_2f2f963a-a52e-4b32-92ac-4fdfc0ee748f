# Entity Relationship Diagram - Swickard EDW Silver Layer

## 🗄️ **Silver Layer Data Model ERD**

This diagram shows the complete entity relationship model for the Silver layer, including all hub tables, satellite tables, and link tables with their relationships and key attributes.

```mermaid
erDiagram
    %% Hub Tables (Core Business Entities)
    SILVER_CUSTOMER {
        bigint CustomerID PK
        varchar CustomerKey UK "Business Key"
        varchar SourceSystem
        datetime CreatedDate
        datetime ModifiedDate
        varchar RecordStatus
    }
    
    SILVER_VEHICLE {
        bigint VehicleID PK
        varchar VIN UK "Vehicle Identification Number"
        varchar VehicleKey UK "Business Key"
        varchar StockNumber
        int VehicleYear
        varchar VehicleMake
        varchar VehicleModel
        varchar VehicleStyle
        varchar StockType "New/Used"
        varchar SourceSystem
        datetime CreatedDate
        datetime ModifiedDate
    }
    
    SILVER_DEAL {
        bigint DealID PK
        varchar DealKey UK "Business Key"
        varchar DealNumber
        varchar DealStatus
        varchar DealType
        datetime DealDate
        varchar SourceSystem
        datetime CreatedDate
        datetime ModifiedDate
    }
    
    SILVER_PERSON {
        bigint PersonID PK
        varchar PersonKey UK "Business Key"
        varchar FirstName
        varchar LastName
        varchar MiddleName
        varchar Suffix
        varchar Gender
        date DateOfBirth
        varchar SourceSystem
        datetime CreatedDate
        datetime ModifiedDate
    }
    
    SILVER_ORGANIZATION {
        bigint OrganizationID PK
        varchar OrganizationKey UK "Business Key"
        varchar OrganizationName
        varchar OrganizationType
        varchar TaxID
        varchar SourceSystem
        datetime CreatedDate
        datetime ModifiedDate
    }
    
    %% Satellite Tables (Descriptive Attributes)
    SILVER_CUSTOMER_OPTOUT {
        bigint OptOutID PK
        bigint CustomerID FK
        varchar OptOutType
        bit IsOptedOut
        datetime OptOutDate
        varchar OptOutReason
        varchar ConsentSource
        datetime EffectiveDate
        datetime EndDate
        varchar RecordSource
    }
    
    SILVER_PERSON_DEMOGRAPHICS_DETAIL {
        bigint PersonDemographicsID PK
        bigint PersonID FK
        varchar MaritalStatus
        varchar EmploymentStatus
        varchar Education
        varchar Income
        varchar Occupation
        varchar Language
        datetime EffectiveDate
        datetime EndDate
        varchar RecordSource
    }
    
    SILVER_VEHICLE_SALE_DETAIL {
        bigint VehicleSaleDetailID PK
        bigint VehicleID FK
        decimal SalePrice
        decimal ListPrice
        decimal InvoicePrice
        datetime SaleDate
        varchar SalesPersonID
        varchar FinancingType
        varchar SaleStatus
        datetime EffectiveDate
        datetime EndDate
        varchar RecordSource
    }
    
    SILVER_VEHICLE_REYNOLDS_DETAIL {
        bigint VehicleReynoldsDetailID PK
        bigint VehicleID FK
        varchar ReynoldsVehicleID
        varchar DealerNumber
        varchar StoreNumber
        varchar AreaNumber
        varchar BusinessUnitName
        varchar ReynoldsStockNumber
        varchar ReynoldsStatus
        datetime LastSyncDate
        datetime EffectiveDate
        datetime EndDate
        varchar RecordSource
    }
    
    SILVER_VEHICLE_ACQUISITION_DETAIL {
        bigint VehicleAcquisitionDetailID PK
        bigint VehicleID FK
        datetime AcquisitionDate
        varchar AcquisitionType
        decimal AcquisitionCost
        varchar AcquisitionSource
        bigint TradeInDealID FK
        varchar AuctionDetails
        datetime EffectiveDate
        datetime EndDate
        varchar RecordSource
    }
    
    SILVER_DEAL_DETAIL {
        bigint DealDetailID PK
        bigint DealID FK
        decimal DealAmount
        decimal DownPayment
        decimal TradeValue
        decimal FinanceAmount
        varchar PaymentMethod
        int TermMonths
        decimal InterestRate
        datetime EffectiveDate
        datetime EndDate
        varchar RecordSource
    }
    
    SILVER_DEAL_FINANCING_DETAIL {
        bigint DealFinancingDetailID PK
        bigint DealID FK
        varchar LenderName
        varchar LoanType
        decimal APR
        int LoanTermMonths
        decimal MonthlyPayment
        varchar CreditScore
        varchar ApprovalStatus
        datetime EffectiveDate
        datetime EndDate
        varchar RecordSource
    }
    
    %% Contact Information Tables
    SILVER_CONTACT_INFO {
        bigint ContactInfoID PK
        varchar ContactType "Email/Phone/Address"
        varchar ContactValue
        bit IsPrimary
        bit IsActive
        datetime CreatedDate
        datetime ModifiedDate
        varchar RecordSource
    }
    
    SILVER_EMAIL_ADDRESS_DETAIL {
        bigint EmailAddressDetailID PK
        bigint ContactInfoID FK
        varchar EmailAddress
        varchar EmailType "Personal/Business"
        bit IsVerified
        bit EmailConsent
        datetime ConsentDate
        datetime EffectiveDate
        datetime EndDate
        varchar RecordSource
    }
    
    SILVER_TELEPHONE_ADDRESS_DETAIL {
        bigint TelephoneAddressDetailID PK
        bigint ContactInfoID FK
        varchar PhoneNumber
        varchar PhoneType "Mobile/Home/Work"
        varchar CountryCode
        bit PhoneConsent
        bit TextConsent
        datetime ConsentDate
        datetime EffectiveDate
        datetime EndDate
        varchar RecordSource
    }
    
    SILVER_POSTAL_ADDRESS_DETAIL {
        bigint PostalAddressDetailID PK
        bigint ContactInfoID FK
        varchar AddressLine1
        varchar AddressLine2
        varchar City
        varchar State
        varchar PostalCode
        varchar Country
        varchar AddressType "Home/Business/Mailing"
        bit MailConsent
        datetime EffectiveDate
        datetime EndDate
        varchar RecordSource
    }
    
    %% Link Tables (Many-to-Many Relationships)
    SILVER_CUSTOMER_CONTACT_INFO_LINK {
        bigint CustomerContactInfoLinkID PK
        bigint CustomerID FK
        bigint ContactInfoID FK
        varchar RelationshipType
        datetime LinkDate
        datetime EffectiveDate
        datetime EndDate
        varchar RecordSource
    }
    
    SILVER_DEAL_CUSTOMER_LINK {
        bigint DealCustomerLinkID PK
        bigint DealID FK
        bigint CustomerID FK
        varchar CustomerRole "Primary/Co-Buyer/Guarantor"
        datetime LinkDate
        datetime EffectiveDate
        datetime EndDate
        varchar RecordSource
    }
    
    SILVER_DEAL_VEHICLE_LINK {
        bigint DealVehicleLinkID PK
        bigint DealID FK
        bigint VehicleID FK
        varchar VehicleRole "Primary/Trade-in"
        datetime LinkDate
        datetime EffectiveDate
        datetime EndDate
        varchar RecordSource
    }
    
    SILVER_PERSON_CUSTOMER_LINK {
        bigint PersonCustomerLinkID PK
        bigint PersonID FK
        bigint CustomerID FK
        varchar RelationshipType "Individual/Spouse/Dependent"
        datetime LinkDate
        datetime EffectiveDate
        datetime EndDate
        varchar RecordSource
    }
    
    SILVER_ORGANIZATION_CUSTOMER_LINK {
        bigint OrganizationCustomerLinkID PK
        bigint OrganizationID FK
        bigint CustomerID FK
        varchar RelationshipType "Employer/Business"
        datetime LinkDate
        datetime EffectiveDate
        datetime EndDate
        varchar RecordSource
    }
    
    SILVER_DEAL_WORKER_LINK {
        bigint DealWorkerLinkID PK
        bigint DealID FK
        bigint WorkerID FK
        varchar WorkerRole "Sales/Finance/Manager"
        datetime LinkDate
        datetime EffectiveDate
        datetime EndDate
        varchar RecordSource
    }
    
    SILVER_DEALERSHIP_DEAL_LINK {
        bigint DealershipDealLinkID PK
        bigint DealershipID FK
        bigint DealID FK
        varchar DealershipRole "Selling/Servicing"
        datetime LinkDate
        datetime EffectiveDate
        datetime EndDate
        varchar RecordSource
    }
    
    %% Hub Relationships
    SILVER_CUSTOMER ||--o{ SILVER_CUSTOMER_OPTOUT : "has"
    SILVER_PERSON ||--o{ SILVER_PERSON_DEMOGRAPHICS_DETAIL : "has"
    SILVER_VEHICLE ||--o{ SILVER_VEHICLE_SALE_DETAIL : "has"
    SILVER_VEHICLE ||--o{ SILVER_VEHICLE_REYNOLDS_DETAIL : "has"
    SILVER_VEHICLE ||--o{ SILVER_VEHICLE_ACQUISITION_DETAIL : "has"
    SILVER_DEAL ||--o{ SILVER_DEAL_DETAIL : "has"
    SILVER_DEAL ||--o{ SILVER_DEAL_FINANCING_DETAIL : "has"
    
    %% Contact Information Relationships
    SILVER_CONTACT_INFO ||--o{ SILVER_EMAIL_ADDRESS_DETAIL : "contains"
    SILVER_CONTACT_INFO ||--o{ SILVER_TELEPHONE_ADDRESS_DETAIL : "contains"
    SILVER_CONTACT_INFO ||--o{ SILVER_POSTAL_ADDRESS_DETAIL : "contains"
    
    %% Link Table Relationships
    SILVER_CUSTOMER ||--o{ SILVER_CUSTOMER_CONTACT_INFO_LINK : "links to"
    SILVER_CONTACT_INFO ||--o{ SILVER_CUSTOMER_CONTACT_INFO_LINK : "links to"
    
    SILVER_DEAL ||--o{ SILVER_DEAL_CUSTOMER_LINK : "links to"
    SILVER_CUSTOMER ||--o{ SILVER_DEAL_CUSTOMER_LINK : "links to"
    
    SILVER_DEAL ||--o{ SILVER_DEAL_VEHICLE_LINK : "links to"
    SILVER_VEHICLE ||--o{ SILVER_DEAL_VEHICLE_LINK : "links to"
    
    SILVER_PERSON ||--o{ SILVER_PERSON_CUSTOMER_LINK : "links to"
    SILVER_CUSTOMER ||--o{ SILVER_PERSON_CUSTOMER_LINK : "links to"
    
    SILVER_ORGANIZATION ||--o{ SILVER_ORGANIZATION_CUSTOMER_LINK : "links to"
    SILVER_CUSTOMER ||--o{ SILVER_ORGANIZATION_CUSTOMER_LINK : "links to"
    
    SILVER_DEAL ||--o{ SILVER_DEAL_WORKER_LINK : "links to"
    SILVER_DEAL ||--o{ SILVER_DEALERSHIP_DEAL_LINK : "links to"
    
    %% Self-Referencing Relationships
    SILVER_VEHICLE ||--o{ SILVER_VEHICLE_ACQUISITION_DETAIL : "trade-in reference"
```

## 🏗️ **Data Model Architecture Patterns**

### **Data Vault 2.0 Pattern**
The Silver layer follows Data Vault 2.0 methodology:

- **Hub Tables**: Core business entities (Customer, Vehicle, Deal, Person, Organization)
- **Satellite Tables**: Descriptive attributes and historical changes
- **Link Tables**: Relationships between business entities

### **Key Design Principles**

#### **1. Hub Tables (Business Keys)**
```sql
-- Standard Hub Table Structure
CREATE TABLE Silver_Customer (
    CustomerID BIGINT IDENTITY(1,1) PRIMARY KEY,
    CustomerKey VARCHAR(50) NOT NULL UNIQUE,  -- Business Key
    SourceSystem VARCHAR(50) NOT NULL,
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    ModifiedDate DATETIME2 DEFAULT GETUTCDATE()
);
```

#### **2. Satellite Tables (Descriptive Data)**
```sql
-- Standard Satellite Table Structure
CREATE TABLE Silver_Customer_OptOut (
    OptOutID BIGINT IDENTITY(1,1) PRIMARY KEY,
    CustomerID BIGINT FOREIGN KEY REFERENCES Silver_Customer(CustomerID),
    -- Business attributes
    OptOutType VARCHAR(50),
    IsOptedOut BIT,
    -- Data Vault metadata
    EffectiveDate DATETIME2 NOT NULL,
    EndDate DATETIME2,
    RecordSource VARCHAR(100) NOT NULL
);
```

#### **3. Link Tables (Relationships)**
```sql
-- Standard Link Table Structure
CREATE TABLE Silver_Deal_Customer_Link (
    DealCustomerLinkID BIGINT IDENTITY(1,1) PRIMARY KEY,
    DealID BIGINT FOREIGN KEY REFERENCES Silver_Deal(DealID),
    CustomerID BIGINT FOREIGN KEY REFERENCES Silver_Customer(CustomerID),
    -- Relationship attributes
    CustomerRole VARCHAR(50),
    -- Data Vault metadata
    EffectiveDate DATETIME2 NOT NULL,
    EndDate DATETIME2,
    RecordSource VARCHAR(100) NOT NULL
);
```

## 🔍 **Business Entity Relationships**

### **Customer-Centric View**
```
Customer (Hub)
├── Person Details (via Person_Customer_Link)
│   └── Demographics (Satellite)
├── Organization Details (via Organization_Customer_Link)
├── Contact Information (via Customer_Contact_Info_Link)
│   ├── Email Details (Satellite)
│   ├── Phone Details (Satellite)
│   └── Address Details (Satellite)
├── Deals (via Deal_Customer_Link)
│   ├── Deal Details (Satellite)
│   ├── Financing Details (Satellite)
│   └── Vehicles (via Deal_Vehicle_Link)
└── Opt-Out Preferences (Satellite)
```

### **Vehicle-Centric View**
```
Vehicle (Hub)
├── Sale Details (Satellite)
├── Reynolds System Details (Satellite)
├── Acquisition Details (Satellite)
└── Deals (via Deal_Vehicle_Link)
    └── Customers (via Deal_Customer_Link)
```

### **Deal-Centric View**
```
Deal (Hub)
├── Deal Details (Satellite)
├── Financing Details (Satellite)
├── Customers (via Deal_Customer_Link)
├── Vehicles (via Deal_Vehicle_Link)
├── Workers/Staff (via Deal_Worker_Link)
└── Dealership (via Dealership_Deal_Link)
```

## 📊 **Data Quality & Constraints**

### **Primary Key Strategy**
- **Surrogate Keys**: BIGINT IDENTITY for all primary keys
- **Business Keys**: Unique constraints on natural business identifiers
- **Composite Keys**: Used in link tables for relationship uniqueness

### **Foreign Key Relationships**
- **Referential Integrity**: All foreign keys enforced
- **Cascade Rules**: Defined based on business requirements
- **Orphan Prevention**: Constraints prevent orphaned records

### **Data Validation Rules**
```sql
-- Example constraints
ALTER TABLE Silver_Vehicle 
ADD CONSTRAINT CK_VIN_Length CHECK (LEN(VIN) = 17);

ALTER TABLE Silver_Vehicle
ADD CONSTRAINT CK_Vehicle_Year 
CHECK (VehicleYear BETWEEN 1900 AND YEAR(GETDATE()) + 2);

ALTER TABLE Silver_Customer_OptOut
ADD CONSTRAINT CK_Effective_Date 
CHECK (EffectiveDate <= ISNULL(EndDate, '9999-12-31'));
```

## 🎯 **Query Patterns & Performance**

### **Common Query Patterns**
```sql
-- Customer 360 View
SELECT 
    c.CustomerKey,
    p.FirstName + ' ' + p.LastName AS FullName,
    ea.EmailAddress,
    pa.City + ', ' + pa.State AS Location,
    COUNT(d.DealID) AS TotalDeals
FROM Silver_Customer c
LEFT JOIN Silver_Person_Customer_Link pcl ON c.CustomerID = pcl.CustomerID
LEFT JOIN Silver_Person p ON pcl.PersonID = p.PersonID
LEFT JOIN Silver_Customer_Contact_Info_Link ccil ON c.CustomerID = ccil.CustomerID
LEFT JOIN Silver_Email_Address_Detail ea ON ccil.ContactInfoID = ea.ContactInfoID
LEFT JOIN Silver_Postal_Address_Detail pa ON ccil.ContactInfoID = pa.ContactInfoID
LEFT JOIN Silver_Deal_Customer_Link dcl ON c.CustomerID = dcl.CustomerID
LEFT JOIN Silver_Deal d ON dcl.DealID = d.DealID
GROUP BY c.CustomerKey, p.FirstName, p.LastName, ea.EmailAddress, pa.City, pa.State;
```

### **Recommended Indexes**
```sql
-- Hub table indexes
CREATE INDEX IX_Silver_Customer_CustomerKey ON Silver_Customer(CustomerKey);
CREATE INDEX IX_Silver_Vehicle_VIN ON Silver_Vehicle(VIN);
CREATE INDEX IX_Silver_Deal_DealKey ON Silver_Deal(DealKey);

-- Link table indexes
CREATE INDEX IX_Deal_Customer_Link_Composite 
ON Silver_Deal_Customer_Link(DealID, CustomerID);

CREATE INDEX IX_Deal_Vehicle_Link_Composite 
ON Silver_Deal_Vehicle_Link(DealID, VehicleID);

-- Satellite table indexes
CREATE INDEX IX_Customer_OptOut_EffectiveDate 
ON Silver_Customer_OptOut(EffectiveDate, EndDate);
```

---

*This ERD represents a comprehensive automotive business data model supporting customer relationship management, vehicle inventory, sales transactions, and compliance requirements.*
