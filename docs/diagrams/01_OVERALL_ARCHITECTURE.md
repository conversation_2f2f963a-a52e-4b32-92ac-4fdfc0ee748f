# Overall Architecture - Swickard EDW

## 🏗️ **High-Level Architecture Overview**

This diagram shows the complete end-to-end architecture of the Swickard Enterprise Data Warehouse, including all data sources, processing layers, and consumption endpoints.

```mermaid
graph TB
    %% External Data Sources
    subgraph "External Data Sources"
        A1[Reynolds DMS<br/>XML Files]
        A2[FTP Servers<br/>Vendor Data]
        A3[CSV Files<br/>Structured Data]
        A4[Third-Party APIs<br/>External Systems]
    end
    
    %% Landing Zone
    subgraph "Landing Zone"
        B1[Azure Data Lake Gen2<br/>sagedw.dfs.core.windows.net]
        B2[Raw File Storage<br/>reynolds/saleslead/yyyy/mm/dd/]
        B3[Staging Areas<br/>Various Formats]
    end
    
    %% Processing Layer
    subgraph "Processing Layer - Azure Data Factory"
        C1[pl_ingest_SalesLead<br/>Direct XML Processing]
        C2[PL_Bronze Pipeline<br/>Orchestration]
        C3[PL_Silver Pipeline<br/>Transformation]
    end
    
    %% Function Apps
    subgraph "Function Apps - Microservices"
        D1[func-swickard-etl-v2<br/>FTPETLPipeline]
        D2[csv-processing-func-3514<br/>CSVProcessingPipeline]
    end
    
    %% Bronze Layer
    subgraph "Bronze Layer - Raw Processed"
        E1[Parquet Files<br/>SampleFIMAST_bronzefile]
        E2[Parquet Files<br/>SampleCUSTOMER_ACCTG_bronzefile]
        E3[Raw SQL Tables<br/>ds_sql_raw_reynolds_SalesLeads]
    end
    
    %% Silver Layer
    subgraph "Silver Layer - Business Entities"
        F1[Silver_Customer<br/>Hub Table]
        F2[Silver_Vehicle<br/>Hub Table]
        F3[Silver_Deal<br/>Hub Table]
        F4[Silver_*Detail Tables<br/>Satellite Tables]
        F5[Silver_*Link Tables<br/>Junction Tables]
    end
    
    %% Gold Layer
    subgraph "Gold Layer - Analytics Ready"
        G1[Enterprise Data Warehouse<br/>AzureSqlDatabaseEDW]
        G2[Dimensional Models<br/>Star Schema]
        G3[Aggregated Views<br/>Business Metrics]
    end
    
    %% Consumption Layer
    subgraph "Consumption Layer"
        H1[Power BI<br/>Dashboards]
        H2[Reporting Services<br/>Operational Reports]
        H3[APIs<br/>Application Integration]
        H4[Data Science<br/>ML Workloads]
    end
    
    %% Data Flow Connections
    A1 --> B1
    A1 --> C1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    
    B1 --> C2
    B2 --> C1
    B3 --> D1
    
    C2 --> D1
    D1 --> D2
    C1 --> E3
    D1 --> E1
    D2 --> E2
    
    C3 --> F1
    C3 --> F2
    C3 --> F3
    E1 --> F1
    E2 --> F2
    E3 --> F1
    
    F1 --> G1
    F2 --> G1
    F3 --> G1
    F4 --> G2
    F5 --> G2
    
    G1 --> H1
    G2 --> H1
    G3 --> H2
    G1 --> H3
    G2 --> H4
    
    %% Styling
    classDef sourceStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef landingStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef processStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef functionStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef bronzeStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef silverStyle fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef goldStyle fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    classDef consumeStyle fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    
    class A1,A2,A3,A4 sourceStyle
    class B1,B2,B3 landingStyle
    class C1,C2,C3 processStyle
    class D1,D2 functionStyle
    class E1,E2,E3 bronzeStyle
    class F1,F2,F3,F4,F5 silverStyle
    class G1,G2,G3 goldStyle
    class H1,H2,H3,H4 consumeStyle
```

## 📋 **Architecture Components**

### **Data Sources (Blue)**
- **Reynolds DMS**: Primary automotive dealer management system
- **FTP Servers**: Various vendor data feeds
- **CSV Files**: Structured data from multiple sources
- **Third-Party APIs**: External system integrations

### **Landing Zone (Purple)**
- **Azure Data Lake Gen2**: Primary storage for raw data
- **Organized Structure**: Date-partitioned folders for efficient processing
- **Multiple Formats**: XML, CSV, JSON, and other file types

### **Processing Layer (Green)**
- **Azure Data Factory**: Orchestration and data movement
- **Three Main Pipelines**: Sales lead ingestion, bronze processing, silver transformation
- **Managed Integration Runtime**: Secure connectivity

### **Function Apps (Orange)**
- **Microservice Architecture**: Specialized processing for different data types
- **Serverless Compute**: Cost-effective and scalable processing
- **Sequential Processing**: Coordinated execution flow

### **Bronze Layer (Pink)**
- **Raw Processed Data**: Minimal transformation, preserving source structure
- **Parquet Format**: Optimized for analytics workloads
- **Data Validation**: Basic quality checks and error handling

### **Silver Layer (Teal)**
- **Business Entities**: Customer, Vehicle, Deal domain objects
- **Data Vault Pattern**: Hub, satellite, and link table structure
- **Data Quality**: Comprehensive validation and cleansing

### **Gold Layer (Yellow)**
- **Analytics Ready**: Dimensional models and aggregated data
- **Star Schema**: Optimized for reporting and analytics
- **Performance Optimized**: Indexed and partitioned for fast queries

### **Consumption Layer (Light Green)**
- **Multiple Interfaces**: Dashboards, reports, APIs, and ML workloads
- **Self-Service Analytics**: Empowering business users
- **Real-Time and Batch**: Supporting various consumption patterns

## 🔄 **Data Flow Patterns**

### **Real-Time Path**
```
Reynolds XML → Direct Ingestion → Silver Layer → Gold Layer → Dashboards
```

### **Batch Processing Path**
```
FTP/CSV Sources → Landing → Function Apps → Bronze → Silver → Gold → Reports
```

### **Hybrid Processing**
- **Hot Path**: Critical sales lead data for immediate processing
- **Cold Path**: Historical and analytical data for batch processing
- **Lambda Architecture**: Supporting both real-time and batch analytics

## 🎯 **Key Architectural Principles**

1. **Separation of Concerns**: Each layer has a specific purpose
2. **Scalability**: Serverless and cloud-native components
3. **Security**: Managed VNet and identity-based authentication
4. **Flexibility**: Multiple ingestion patterns for different data sources
5. **Performance**: Optimized storage formats and processing patterns
6. **Maintainability**: Clear data lineage and transformation logic

## 📊 **Business Value**

- **360° Customer View**: Unified customer data across all touchpoints
- **Real-Time Insights**: Immediate visibility into sales leads and opportunities
- **Operational Efficiency**: Automated data processing and quality management
- **Scalable Foundation**: Architecture supports business growth and new data sources
- **Compliance Ready**: Audit trails and data governance capabilities

---

*This architecture supports Swickard's automotive business with a modern, scalable data platform that enables both operational efficiency and strategic analytics.*
