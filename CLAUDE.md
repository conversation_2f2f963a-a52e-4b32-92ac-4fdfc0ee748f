# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Testing & Validation
```powershell
# Run all tests
./tests/run-tests.ps1

# Run specific test categories
./tests/run-tests.ps1 -Category Unit
./tests/run-tests.ps1 -Category Integration
./tests/run-tests.ps1 -Category Validation
./tests/run-tests.ps1 -Category Performance

# Validate ADF artifacts before deployment
./deployment/validate.ps1

# Generate test reports
./tests/run-tests.ps1 -Category All -GenerateReport
```

### Development & Deployment
```powershell
# Setup development environment
./deployment/setup-dev-environment.ps1

# Deploy to environments
./deployment/deploy.ps1 -Environment dev
./deployment/deploy.ps1 -Environment test
./deployment/deploy.ps1 -Environment prod

# Start/stop local development services
docker-compose up -d
docker-compose down
```

### VS Code Tasks
Available via VS Code task runner (Ctrl+Shift+P > "Tasks: Run Task"):
- Validate ADF Artifacts
- Deploy to Dev/Test
- Run Unit/Integration Tests
- Setup Development Environment
- Start/Stop Local Services

## Architecture

### Overall System Design
This is an **Azure Data Factory (ADF) implementation** for automotive dealership data integration, following a **medallion architecture** (Bronze/Silver/Gold) with **Data Vault 2.0 patterns**. The system processes multiple data sources including Reynolds DMS XML feeds, FTP vendor data, and CSV files.

### Key Components

#### 1. Data Processing Layers
- **Landing Zone**: Raw data ingestion to Azure Data Lake Gen2 (`sagedw.dfs.core.windows.net`)
- **Bronze Layer**: Minimal processing with parquet optimization
- **Silver Layer**: Business entities with Data Vault structure (Hub/Satellite/Link tables)
- **Gold Layer**: Analytics-ready dimensional models in Azure SQL Database

#### 2. Core Pipelines
- **`pl_ingest_SalesLead`**: Real-time Reynolds XML processing (37 field mappings)
- **`PL_Bronze`**: Batch processing orchestration via Azure Functions
- **`PL_Silver`**: Business entity transformation to Data Vault model

#### 3. Function Apps (Microservice Architecture)
- **`func-swickard-etl-v2`**: Primary ETL processing for FTP data retrieval
- **`csv-processing-func-3514`**: Specialized CSV file processing and validation
- Sequential execution pattern: Bronze pipeline → func-swickard-etl-v2 → csv-processing-func-3514

#### 4. Data Model
27+ business entities including:
- **Customer Hub**: Core customer identity with satellite tables for details
- **Vehicle Hub**: VIN-based vehicle tracking with specifications
- **Deal Hub**: Transaction management with customer/vehicle relationships
- **Link Tables**: Junction tables connecting entities (Customer-Deal-Vehicle relationships)

### Data Flow Patterns
```
External Sources (Reynolds XML, FTP, CSV)
    → Landing Zone (Data Lake)
    → Function Apps (ETL Processing)
    → Bronze Layer (Parquet Files)
    → Silver Layer (Business Entities)
    → Gold Layer (Analytics Tables)
```

#### Real-time vs Batch Processing
- **Real-time**: Reynolds XML → Direct ingestion → Silver layer (< 5 minutes)
- **Batch**: FTP/CSV sources → Function apps → Bronze → Silver (30-45 minutes)

### Security Architecture
- **Authentication**: Azure AD with Managed Identity
- **Network**: Private endpoints and Managed VNet integration
- **Secrets**: Azure Key Vault for all sensitive configuration
- **Compliance**: GDPR consent tracking with audit trails

**⚠️ Known Security Issue**: Function apps currently use anonymous authentication - requires remediation with function keys or managed identity.

### Environment Structure
- **Development**: Local development with AI-assisted capabilities
- **Test**: Integration testing with automated validation
- **Production**: SwickardEDW Data Factory in West US region

## Project Structure

```
swickard-adf/
├── src/datafactory/          # ADF artifacts (Git-connected to Azure)
│   ├── pipeline/            # Pipeline definitions (JSON)
│   ├── dataset/             # Dataset definitions
│   ├── linkedService/       # Connection configurations
│   ├── dataflow/           # Data transformation flows
│   ├── trigger/            # Scheduling and event triggers
│   └── integrationRuntime/ # Runtime configurations
├── config/                 # Environment-specific configurations
│   ├── dev.json           # Development environment settings
│   ├── test.json          # Test environment settings
│   └── prod.json          # Production environment settings
├── deployment/            # Deployment scripts and infrastructure
├── tests/                # Test suites with Pester framework
│   ├── unit/            # Unit tests for ADF artifacts
│   ├── integration/     # Integration tests
│   └── performance/     # Performance and load tests
└── docs/                # Architecture and technical documentation
```

### Configuration Management
- **Environment-specific configs**: JSON files in `config/` directory
- **Naming conventions**: Follow Azure standards (`adf-swickard-{env}`, `rg-swickard-adf-{env}`)
- **Required properties**: subscriptionId, projectName, dataFactory, linkedServices, security settings
- **Consistent structure**: All environments share the same configuration schema

### Testing Framework
- **Pester-based testing**: PowerShell testing framework for ADF artifacts
- **Test categories**: Unit, Integration, Validation, Performance
- **Automated validation**: JSON structure validation, naming convention checks, security configuration validation
- **CI/CD integration**: Test results exported to XML for build pipelines
- **Key test file**: `tests/unit/Test-SwickardADFArtifacts.ps1` contains comprehensive unit tests for:
  - Environment configuration validation (dev.json, test.json, prod.json)
  - Azure naming convention compliance
  - Pipeline/Dataset/LinkedService JSON structure validation
  - Security configuration tests (Key Vault, Managed Identity, RBAC)
  - Deployment script existence verification

## Development Workflow

### Feature Development Process
1. Create feature branch from main
2. Develop using VS Code with ADF Studio integration
3. Test locally using debug mode and unit tests
4. Validate artifacts using `./deployment/validate.ps1`
5. Deploy to dev environment for integration testing
6. Submit PR with test results and validation reports

### Prerequisites
- VS Code with Azure Data Factory extensions
- Azure CLI with Data Factory extensions
- PowerShell 7+ with Az modules and azure.datafactory.tools
- Docker for local service dependencies
- Git for version control

### Performance Targets
- **Sales Lead Processing**: < 5 minutes per XML file
- **Bronze Pipeline**: 30-45 minutes for full batch processing
- **Silver Pipeline**: 15-30 minutes for transformation
- **End-to-End Latency**: < 2 hours for batch data availability

## Key Files & Patterns

### Critical Configuration Files
- `config/{environment}.json`: Environment-specific settings including subscription, resource names, and security configuration
- `.vscode/tasks.json`: VS Code task definitions for common development operations
- `tests/run-tests.ps1`: Comprehensive test runner with multiple test categories

### ADF Artifact Patterns
- **Pipeline naming**: Use descriptive prefixes (`pl_ingest_`, `PL_Bronze`, `PL_Silver`)
- **Dataset naming**: Include source system and data type (`ds_sql_raw_reynolds_SalesLeads`)
- **Linked service naming**: Prefix with service type (`LS_ADLS_`, `LS_SQL_`, `LS_Func_`)
- **Error handling**: Implement retry logic with exponential backoff and dead letter queues

### Reporting Work Performed Guidelines
When documenting changes or work performed:

**ALWAYS Show:**
- JSON configuration changes in ADF artifacts
- SQL DDL statements and schema modifications
- Pipeline definition updates and field mappings
- Configuration file modifications (dev.json, test.json, prod.json)

**Example - ADF Mapping Changes:**
```json
// Added new field mappings to pipeline
"translator": {
  "mappings": [
    {
      "source": { "path": "['ProspectId']" },
      "sink": { "name": "ProspectId", "type": "Int64" }
    },
    {
      "source": { "path": "['ConsentEmail']" },
      "sink": { "name": "EmailConsent", "type": "Boolean" }
    }
  ]
}
```

**AVOID Showing:**
- Raw XML/CSV source data examples
- Sample records or data content
- Source data formats and structures

**Exception:** Only show source data structure when specifically asked "what does the data look like?" or when mapping FROM source formats is the specific question.

### Data Vault Implementation
- **Hub tables**: `Silver_{Entity}` (Customer, Vehicle, Deal)
- **Satellite tables**: `Silver_{Entity}Detail` for descriptive attributes
- **Link tables**: `Silver_{Entity1}{Entity2}Link` for relationships
- **Business keys**: Natural keys for entity identification
- **Audit columns**: CreatedDate, ModifiedDate, RecordStatus for all tables